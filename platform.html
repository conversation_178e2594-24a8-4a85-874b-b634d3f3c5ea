<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>bor AureaVoice</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc; /* slate-50 */
        }
        .chart-container {
            position: relative;
            width: 100%;
            height: 300px;
        }
        .main-gradient {
             background: linear-gradient(135deg, #0079FF, #004AAD);
        }
        .category-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        /* --- Struktur CSS Grid --- */
        .dashboard-grid {
            display: grid;
            gap: 1rem; /* Gap dikurangi setengahnya */
            grid-template-columns: 1fr;
            grid-template-areas:
                "main"
                "sidebar";
        }

        @media (min-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: repeat(3, 1fr);
                grid-template-areas: "main main sidebar";
            }
        }

        .main-column {
            grid-area: main;
        }

        .sidebar-column {
            grid-area: sidebar;
        }
        
        .category-grid {
            display: grid;
            gap: 1rem;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(2, 1fr);
        }

        .category-column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        @media (max-width: 768px) {
            .category-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
            }
        }
    </style>
</head>
<body class="text-gray-800">

    <div id="app">
        <!-- Navigasi -->
        <nav class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-10">
            <div class="container mx-auto px-6 py-4 flex justify-between items-center">
                <div class="text-2xl font-bold text-blue-600">AureaVoice</div>
                <div class="flex items-center">
                    <a href="#" class="bg-slate-700 text-white px-4 py-2 rounded-lg hover:bg-slate-800 transition-colors text-sm font-semibold">Keluar</a>
                </div>
            </div>
        </nav>

        <!-- Konten Utama -->
        <main class="container mx-auto p-4 md:p-8">
            <!-- Header Selamat Datang -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Selamat datang kembali, Budi!</h1>
                <p class="text-gray-500 mt-1">Teruslah berlatih, konsistensi adalah kunci untuk mencapai aksen yang natural.</p>
            </div>

            <!-- Grid Utama Dasbor menggunakan CSS Grid -->
            <div class="dashboard-grid">
                
                <!-- Kolom Utama (Kiri) -->
                <div class="main-column flex flex-col space-y-4">
                    <!-- Kartu Rekomendasi Latihan -->
                    <div class="bg-white rounded-xl shadow-lg p-6 border border-slate-100">
                        <h2 class="text-xl font-bold mb-4">Rekomendasi Latihan Utama Untuk Anda</h2>
                        <div class="space-y-4">
                            <div class="flex items-center p-4 bg-blue-50 rounded-lg border border-blue-100">
                                <span class="text-3xl mr-4">🎵</span>
                                <div class="flex-grow">
                                    <p class="font-semibold text-blue-800">Irama dan Penekanan</p>
                                    <p class="text-sm text-slate-600 mt-1">Fokus pada ritme *stress-timed* untuk alur bicara yang lebih natural.</p>
                                </div>
                                <button class="ml-4 bg-blue-600 text-white font-semibold px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm flex-shrink-0">Mulai Latihan</button>
                            </div>
                        </div>
                    </div>
                    <!-- Kartu Grafik Perkembangan -->
                    <div class="bg-white rounded-xl shadow-lg p-6 border border-slate-100">
                        <h2 class="text-xl font-bold mb-4">Perkembangan Skor Anda</h2>
                        <div class="chart-container">
                            <canvas id="progressChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Kolom Samping (Kanan) -->
                <div class="sidebar-column flex flex-col">
                    <!-- Kartu Profil & Skor Pengguna -->
                    <div class="bg-white rounded-xl shadow-lg border border-slate-100 flex flex-col h-full">
                        <div class="main-gradient text-white rounded-t-xl p-6 text-center">
                            <p class="text-blue-200 text-lg">Skor Aksen Anda</p>
                            <p class="text-6xl font-black my-2">82<span class="text-3xl font-bold">%</span></p>
                            <p class="text-blue-200 text-sm">meningkat 2% dari minggu lalu!</p>
                        </div>
                        <div class="p-6 flex-grow flex flex-col">
                            <h3 class="font-bold text-lg text-center mb-4">Statistik Budi</h3>
                            <div class="mx-auto mb-4">
                                <img src="https://placehold.co/100x100/E2E8F0/475569?text=B" alt="Foto Profil Budi" class="w-24 h-24 rounded-full border-4 border-white shadow-md">
                            </div>
                            <div class="space-y-2 mt-2 border-t pt-4">
                                <div class="flex justify-between items-center bg-slate-100 p-2 rounded-md">
                                    <span class="text-sm text-slate-600">Latihan Selesai</span>
                                    <span class="font-bold text-slate-800 text-base">128</span>
                                </div>
                                <div class="flex justify-between items-center bg-slate-100 p-2 rounded-md">
                                    <span class="text-sm text-slate-600">Waktu Latihan</span>
                                    <span class="font-bold text-slate-800 text-base">14 Jam</span>
                                </div>
                                <div class="flex justify-between items-center bg-slate-100 p-2 rounded-md">
                                    <span class="text-sm text-slate-600">Kategori Dicoba</span>
                                    <span class="font-bold text-slate-800 text-base">5/7</span>
                                </div>
                                <div class="flex justify-between items-center bg-slate-100 p-2 rounded-md">
                                    <span class="text-sm text-slate-600">Kategori Dikuasai</span>
                                    <span class="font-bold text-slate-800 text-base">2</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Grid Kategori Latihan -->
            <div class="mt-12">
                <h2 class="text-2xl font-bold mb-4">Pilih Kategori Latihan Lainnya</h2>
                <div class="category-grid">
                    
                    <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">👄</div>
                        <h3 class="font-bold text-lg">Peta Vokal Amerika</h3>
                        <p class="text-sm text-gray-500 mt-1">Kuasai bunyi vokal seperti pada 'cat' dan 'cut'.</p>
                    </a>
                    
                     <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">↔️</div>
                        <h3 class="font-bold text-lg">Pasangan Minimal</h3>
                        <p class="text-sm text-gray-500 mt-1">Bedakan bunyi seperti 'ship' vs 'sheep'.</p>
                    </a>
                    
                    <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">🔡</div>
                        <h3 class="font-bold text-lg">Gugus Konsonan</h3>
                        <p class="text-sm text-gray-500 mt-1">Ucapkan kata seperti 'strengths' dan 'world'.</p>
                    </a>

                    <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">📖</div>
                        <h3 class="font-bold text-lg">Membaca Paragraf</h3>
                        <p class="text-sm text-gray-500 mt-1">Latih kelancaran dan intonasi dalam konteks.</p>
                    </a>

                    <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">💬</div>
                        <h3 class="font-bold text-lg">Skenario Dunia Nyata</h3>
                        <p class="text-sm text-gray-500 mt-1">Simulasi percakapan sehari-hari.</p>
                    </a>

                     <a href="#" class="category-card block bg-white p-6 rounded-xl border border-slate-200">
                        <div class="text-3xl mb-3">🔥</div>
                        <h3 class="font-bold text-lg">Latihan Intensif</h3>
                        <p class="text-sm text-gray-500 mt-1">Tantang diri Anda dengan kalimat acak.</p>
                    </a>
                    
                </div>
            </div>

        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const progressCtx = document.getElementById('progressChart').getContext('2d');
            
            const brandColors = {
                brightBlue: '#0079FF',
                deepBlue: '#004AAD',
            };

            new Chart(progressCtx, {
                type: 'line',
                data: {
                    labels: ['4 Minggu Lalu', '3 Minggu Lalu', '2 Minggu Lalu', 'Minggu Lalu', 'Minggu Ini'],
                    datasets: [{
                        label: 'Skor Aksen',
                        data: [75, 78, 77, 80, 82],
                        backgroundColor: 'rgba(0, 121, 255, 0.1)',
                        borderColor: brandColors.brightBlue,
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: brandColors.deepBlue,
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 60,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                         tooltip: {
                            callbacks: {
                                title: function(tooltipItems) {
                                    const item = tooltipItems[0];
                                    let label = item.chart.data.labels[item.dataIndex];
                                    return Array.isArray(label) ? label.join(' ') : label;
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>
